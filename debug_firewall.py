#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙调试脚本
用于诊断IP添加失败的问题
"""

from firewall_manager import FirewallManager
import urllib3
import urllib.parse

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def debug_firewall_operations():
    """调试防火墙操作"""
    print("=" * 60)
    print("防火墙操作调试")
    print("=" * 60)
    
    # 创建防火墙管理器
    fw_manager = FirewallManager("*************", "wabsuper", "whgD@955989")
    
    print("\n【步骤1】尝试登录")
    print("-" * 40)
    
    login_success = fw_manager.login()
    if login_success:
        print(f"✅ 登录成功，session_id: {fw_manager.session_id}")
    else:
        print("❌ 登录失败")
        return
    
    print("\n【步骤2】查询当前IP列表")
    print("-" * 40)
    
    current_ips = fw_manager.get_blocked_ips()
    print(f"📋 当前IP列表: {current_ips}")
    print(f"📊 IP数量: {len(current_ips)}")
    
    print("\n【步骤3】准备添加新IP")
    print("-" * 40)
    
    new_ip = "*******"
    print(f"🎯 要添加的IP: {new_ip}")
    
    # 检查IP是否已存在
    if new_ip in current_ips:
        print(f"ℹ️  IP {new_ip} 已在列表中，先移除进行测试")
        # 这里可以添加移除逻辑，但为了简单起见，我们使用不同的IP
        new_ip = "*******"
        print(f"🔄 改用测试IP: {new_ip}")
    
    print("\n【步骤4】构造请求数据")
    print("-" * 40)
    
    # 模拟add_blocked_ip方法的逻辑，但添加调试信息
    updated_ips = current_ips + [new_ip]
    print(f"📝 更新后的IP列表: {updated_ips}")
    
    # 构造POST数据
    post_data = [
        ('def_host_name', fw_manager.host_name),
        ('def_host_mac', '00:00:00:00:00:00'),
        ('host_ipad_input', new_ip),
        ('name_hidden', fw_manager.host_name),
        ('def_host_frompage', ''),
        ('def_host_from', ''),
        ('def_host_edt_but', ' 确定 ')
    ]
    
    # 添加所有IP地址
    for ip in updated_ips:
        post_data.append(('def_host_ipad', ip))
    
    print("📤 POST数据:")
    for key, value in post_data:
        print(f"   {key}: {value}")
    
    # 构造URL
    encoded_name = urllib.parse.quote(fw_manager.host_name, encoding='gb2312')
    submit_url = f"https://{fw_manager.host}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name={encoded_name}"
    print(f"🔗 提交URL: {submit_url}")
    
    print("\n【步骤5】发送请求")
    print("-" * 40)
    
    try:
        # 发送POST请求
        response = fw_manager.session.post(submit_url, data=post_data, verify=False)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        # 检查响应内容
        if response.status_code == 200:
            print("✅ 请求成功发送")
            
            # 检查响应内容中是否有错误信息
            response_text = response.text
            print(f"📄 响应长度: {len(response_text)} 字符")
            
            # 查找可能的错误信息
            if "错误" in response_text or "error" in response_text.lower():
                print("⚠️  响应中可能包含错误信息")
                # 提取错误相关的部分
                lines = response_text.split('\n')
                for i, line in enumerate(lines):
                    if "错误" in line or "error" in line.lower():
                        print(f"   第{i+1}行: {line.strip()}")
            
            # 检查是否有重定向指示
            if "window.location" in response_text:
                print("🔄 检测到页面重定向")
                import re
                redirect_match = re.search(r'window\.location\s*=\s*["\']([^"\']+)["\']', response_text)
                if redirect_match:
                    redirect_url = redirect_match.group(1)
                    print(f"   重定向到: {redirect_url}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
    
    except Exception as e:
        print(f"❌ 请求发送失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n【步骤6】验证结果")
    print("-" * 40)
    
    # 重新查询IP列表验证
    try:
        updated_list = fw_manager.get_blocked_ips()
        print(f"📋 验证后的IP列表: {updated_list}")
        
        if new_ip in updated_list:
            print(f"✅ 成功！IP {new_ip} 已添加到列表中")
        else:
            print(f"❌ 失败！IP {new_ip} 未在列表中找到")
            
        # 比较前后差异
        added_ips = set(updated_list) - set(current_ips)
        removed_ips = set(current_ips) - set(updated_list)
        
        if added_ips:
            print(f"➕ 新增IP: {list(added_ips)}")
        if removed_ips:
            print(f"➖ 移除IP: {list(removed_ips)}")
        if not added_ips and not removed_ips:
            print("🔄 IP列表无变化")
            
    except Exception as e:
        print(f"❌ 验证查询失败: {e}")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    debug_firewall_operations()
