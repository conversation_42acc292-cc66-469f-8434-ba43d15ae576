GET /cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name=01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7 HTTP/1.1
Host: *************
Connection: keep-alive
sec-ch-ua: "Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: frame
Referer: https://*************/cgi/maincgi.cgi?Url=HostObj
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9
Cookie: session_id_443=MTU1OTc3NTYzMzUwNTM=

HTTP/1.1 200 OK
Date: Thu, 10 Jul 2025 03:24:13 GMT
Server: TOPSEC
Cache-Control: no-cache
Pragma: no-cache
X-Frame-Options: SAMEORIGIN
Keep-Alive: timeout=5, max=100
Connection: Keep-Alive
Content-Type: text/html; charset=utf-8
Content-Length: 6015

<HTML>
<HEAD>
 <LINK REL=stylesheet HREF="/site/css/style.css" TYPE="text/css"/>
  <LINK REL=stylesheet HREF="/site/css/main.css" TYPE="text/css"/>
<SCRIPT SRC="/site/js/checkinput.js"></SCRIPT>
<SCRIPT SRC="/site/js/deleteelm.js"></SCRIPT>
<SCRIPT SRC="/site/js/function.js"></SCRIPT>
<SCRIPT SRC="/site/js/js_basement.js"></SCRIPT>
<SCRIPT type='text/javascript'  SRC="/site/js/sv_js.js"></SCRIPT>
<SCRIPT language="javascript">

function opCancel() {
	if (window.opener)
		window.close();	
	else
		history.back(1);
}
</SCRIPT>

</HEAD>
<BODY>
<script language="JavaScript">
<!--
// -->
</script>
			<div class="right_container tab_container">
<table class="tab_content" cellspacing="0"width="100%">
<tr><td style="padding:5px">
<script src="/site/js/function.js"></script>
<SCRIPT LANGUAGE="JavaScript">
<!--
function ip_adtolist()
{
	var form = host_form;
	def_host_ipad = form.def_host_ipad;
	host_ipad_input = form.host_ipad_input;
	var len = def_host_ipad.length;
	if( !ipCheck(form.host_ipad_input.value) )
	{
		alert("IP地址输入有误，请重新输入");
		form.host_ipad_input.focus();
		return false;
	}
	if ( host_ipad_input != null ) 
	{
		var found = false;
		for(var count = 0; count < len; count++) 
		{
			if (def_host_ipad.options[count] != null) 
			{
				if (host_ipad_input.value == def_host_ipad.options[count].value) 
				{
					found = true;
					break;
				}
			}
		}
		if (found != true) 
		{
			def_host_ipad.options[len] = new Option(host_ipad_input.value);
			def_host_ipad.options[len].value = host_ipad_input.value;
			def_host_ipad.options[len].selected = true;
			len++;
		}
	}
}

function ip_delflist()
{
	var form = host_form;
	var def_host_ipad  = form.def_host_ipad;
	var len = def_host_ipad.options.length;
	for(var i = (len-1); i >= 0; i--) 
	{
		if ((def_host_ipad.options[i] != null) && (def_host_ipad.options[i].selected == true)) 
		{
			def_host_ipad.options[i] = null;
		}
	}
}

function getSelectCount(objSelect)
{
	var count = 0;
	for(var i=0;i<objSelect.options.length;i++)
	{
		var el = objSelect.options[i];
		if(el.selected)
		{
			count++;
		}
	}
	return count;
}

function DetectMac()
{
	var mac_obj = document.getElementById("def_host_mac");
	var ip_obj = document.getElementById("host_iplist_id");
	var cnt = getSelectCount(ip_obj);
	if(cnt != 1)
	{
		alert("请在IP地址中选中一个IP地址");
		return false;
	}
	$.get("/cgi/maincgi.cgi",{Url:"Ajax",Act:"DetectMac",IP:ip_obj.value},function(data){
		if(data && macCheck(data))
		{
			mac_obj.value = data;			
		}
		else
		{
			alert("未探测到该IP对应的MAC地址");
		}
	});
}

function submitCheck()
{
	var form = host_form;
	if(form.def_host_name.value =="")
	{
		alert("名称不能为空，请输入名称");
		form.def_host_name.focus();
		return false;
	}
	if( !chkSafe(form.def_host_name.value))
	{
		alert("输入名称出现特殊字符，请重新输入");
		form.def_host_name.focus();
		return false;
	}
	if( !ipCheck(form.def_host_ipad.value) && form.def_host_ipad.value != "" )
	{
		alert("IP地址输入有误，请重新输入");
		form.def_host_ipad.focus();
		return false;
	}
	sort_ascend("host_iplist_id");
	all_option_select(form.def_host_ipad);
	return true;
}
//-->
</SCRIPT>

<form name="host_form" id="host_form" method="post" action="">

<table class="borderTable" cellspacing="0" cellpadding="0">
 <TR> 
  <TD class="TDtopic" > 主机属性 </TD>  
 </TR>

 <tr>
 <td><hr></td>
 </tr>

 <tr><td class="body"><table>
   <tr>
        <td  width="30%" class="content_new">名称</td>
        <td colspan="3"> 
          <input class="text" type="text" maxLength="30" name="def_host_name" value="01-网安封禁内网地址"  style="width:170px" onblur="checkName('host_form','tip',this);">&nbsp;<FONT COLOR="red" id="tip">*</FONT>
        </td>
      </tr>
   <tr> 
        <td class="content_new">物理地址</td>
        <td colspan="3"> 
          <input class="text" type="text"  value="00:00:00:00:00:00"  style="width:170px" maxLength="17" name="def_host_mac" id="def_host_mac" onKeyUp="value=value.replace(/[^\0120123456789ABCDEFabcdef:]/g,'') "onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\0120123456789ABCDEFabcdef:]/g,''))" >
          <input class="button" type="button" name="detect_mac" id="detect_mac" value="探测" onClick="javascript:DetectMac();">
        </td>          
   </tr>
   <tr> 
            <td class="content_new" valign="top">IP地址</td>
            <td width="122" valign="top">
			
              <select size="8" multiple name="def_host_ipad" id="host_iplist_id" style="width:170">
              </select>
<SCRIPT LANGUAGE="JavaScript">
<!--
var ip_str = '******* ************* *********** *********** *******  ';
var iplist_id = document.getElementById("host_iplist_id");
addSelElement( ip_str, iplist_id);
//-->
</SCRIPT>
            </td>
            <td width="35px" valign="top"> 
              <input class="button" type="button" name="srcInsBut" value="<-" onClick="javascript:ip_adtolist()">
              <input class="button" type="button" name="srcDelBut" value="×" onClick="javascript:ip_delflist()">
              </td>
              <td valign="top"><input class="text" type="text" maxlength="15" name="host_ipad_input">              
            </td>
            </tr>
   
 </table></td></tr>
 </TABLE> 
	
  <table class="buttonTable">
    <tr> 
      <td><hr></td>
    </tr>
    <tr>
    <td> 
      <input type="hidden" class="text" name="name_hidden" value="01-网安封禁内网地址">
      <input type="hidden" class="text" name="def_host_frompage" value="">
      <input type="hidden" class="text" name="def_host_from" value="">
      <input name="def_host_edt_but" type="submit" class="confirm" onClick="return submitCheck()"  value=" 确定 " size="600">
      <input class="cancel" type="button" name="button" value=" 取消 " onClick="javascript:opCancel()">
    </td>
    </tr>
  </table>
</form>
    </td>
   </tr>
  </table>
 </div>
</BODY>
</HTML>
