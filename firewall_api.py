#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙API接口
提供查询已封禁IP和封禁新IP的简单接口
"""

from firewall_manager import FirewallManager
import json
from typing import List, Dict, Any


class FirewallAPI:
    """防火墙API类，提供简化的接口"""
    
    def __init__(self, host: str = "*************", username: str = "wabsuper", password: str = "whgD@955989"):
        """
        初始化防火墙API
        
        Args:
            host: 防火墙主机地址，默认从抓包数据获取
            username: 用户名，默认从抓包数据获取
            password: 密码，默认从抓包数据获取（URL解码后）
        """
        self.fw_manager = FirewallManager(host, username, password)
        self._logged_in = False
    
    def _ensure_login(self) -> bool:
        """确保已登录"""
        if not self._logged_in:
            self._logged_in = self.fw_manager.login()
        return self._logged_in
    
    def get_blocked_ips(self) -> Dict[str, Any]:
        """
        接口1: 查询已封禁的所有IP
        
        Returns:
            Dict: 包含状态和IP列表的字典
            {
                "success": bool,
                "message": str,
                "data": {
                    "blocked_ips": List[str],
                    "count": int
                }
            }
        """
        try:
            if not self._ensure_login():
                return {
                    "success": False,
                    "message": "登录失败",
                    "data": None
                }
            
            blocked_ips = self.fw_manager.get_blocked_ips()
            
            return {
                "success": True,
                "message": "查询成功",
                "data": {
                    "blocked_ips": blocked_ips,
                    "count": len(blocked_ips)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "data": None
            }
    
    def block_ip(self, ip: str) -> Dict[str, Any]:
        """
        接口2: 封禁新的IP
        
        Args:
            ip: 要封禁的IP地址
            
        Returns:
            Dict: 包含操作结果的字典
            {
                "success": bool,
                "message": str,
                "data": {
                    "ip": str,
                    "action": str
                }
            }
        """
        try:
            if not self._ensure_login():
                return {
                    "success": False,
                    "message": "登录失败",
                    "data": None
                }
            
            # 验证IP格式
            if not self.fw_manager._validate_ip(ip):
                return {
                    "success": False,
                    "message": f"IP地址格式不正确: {ip}",
                    "data": None
                }
            
            # 检查IP是否已被封禁
            current_ips = self.fw_manager.get_blocked_ips()
            if ip in current_ips:
                return {
                    "success": True,
                    "message": f"IP {ip} 已在封禁列表中",
                    "data": {
                        "ip": ip,
                        "action": "already_blocked"
                    }
                }
            
            # 添加新IP
            success = self.fw_manager.add_blocked_ip(ip)
            
            if success:
                return {
                    "success": True,
                    "message": f"IP {ip} 封禁成功",
                    "data": {
                        "ip": ip,
                        "action": "blocked"
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"IP {ip} 封禁失败",
                    "data": None
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"封禁失败: {str(e)}",
                "data": None
            }


def demo():
    """演示API使用方法"""
    print("=== 防火墙API演示 ===\n")
    
    # 创建API实例
    api = FirewallAPI()
    
    # 接口1: 查询已封禁的IP
    print("1. 查询已封禁的IP:")
    result = api.get_blocked_ips()
    print(json.dumps(result, indent=2, ensure_ascii=False))
    print()
    
    # 接口2: 封禁新IP (示例)
    print("2. 封禁新IP (示例: *************):")
    result = api.block_ip("*************")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    print()
    
    # 再次查询验证
    print("3. 再次查询验证:")
    result = api.get_blocked_ips()
    print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    # 禁用SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    demo()
