#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙IP管理工具
模拟防火墙登录、查询已封禁IP、添加新封禁IP的功能
"""

import requests
import urllib.parse
import re
from typing import List, Dict, Optional
import json


class FirewallManager:
    """防火墙管理类"""
    
    def __init__(self, host: str, username: str, password: str):
        """
        初始化防火墙管理器
        
        Args:
            host: 防火墙主机地址
            username: 用户名
            password: 密码
        """
        self.host = host
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session_id = None
        self.host_name = "01-网安封禁内网地址"  # 从抓包数据中获取的主机名
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def login(self) -> bool:
        """
        登录防火墙
        
        Returns:
            bool: 登录是否成功
        """
        try:
            # 构造登录数据
            login_data = {
                'username': self.username,
                'passwd': self.password,
                'loginSubmitIpt': ''
            }
            
            # 发送登录请求
            login_url = f"https://{self.host}/cgi/maincgi.cgi?Url=Index"
            response = self.session.post(login_url, data=login_data, verify=False)
            
            # 检查是否获取到session_id
            if 'session_id_443' in response.cookies:
                self.session_id = response.cookies['session_id_443']
                print(f"登录成功，获取到session_id: {self.session_id}")
                return True
            else:
                print("登录失败，未获取到session_id")
                return False
                
        except Exception as e:
            print(f"登录过程中发生错误: {e}")
            return False
    
    def get_blocked_ips(self) -> List[str]:
        """
        查询已封禁的IP列表
        
        Returns:
            List[str]: 已封禁的IP地址列表
        """
        if not self.session_id:
            print("请先登录")
            return []
        
        try:
            # 构造查询URL，使用URL编码的主机名
            encoded_name = urllib.parse.quote("01-网安封禁内网地址", encoding='gb2312')
            query_url = f"https://{self.host}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name={encoded_name}"
            
            # 发送查询请求
            response = self.session.get(query_url, verify=False)
            
            if response.status_code == 200:
                # 从响应中提取IP列表
                # 查找JavaScript中的ip_str变量
                ip_pattern = r"var ip_str = '([^']+)';"
                match = re.search(ip_pattern, response.text)
                
                if match:
                    ip_str = match.group(1).strip()
                    # 分割IP字符串，过滤空字符串
                    ip_list = [ip.strip() for ip in ip_str.split() if ip.strip()]
                    print(f"查询到已封禁IP: {ip_list}")
                    return ip_list
                else:
                    print("未找到IP列表数据")
                    return []
            else:
                print(f"查询请求失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"查询过程中发生错误: {e}")
            return []
    
    def add_blocked_ip(self, new_ip: str) -> bool:
        """
        添加新的封禁IP
        
        Args:
            new_ip: 要封禁的新IP地址
            
        Returns:
            bool: 添加是否成功
        """
        if not self.session_id:
            print("请先登录")
            return False
        
        # 验证IP格式
        if not self._validate_ip(new_ip):
            print(f"IP地址格式不正确: {new_ip}")
            return False
        
        try:
            # 先获取当前已封禁的IP列表
            current_ips = self.get_blocked_ips()
            
            # 检查IP是否已存在
            if new_ip in current_ips:
                print(f"IP {new_ip} 已在封禁列表中")
                return True
            
            # 添加新IP到列表
            updated_ips = current_ips + [new_ip]
            
            # 构造POST数据 - 使用列表来处理多个相同名称的字段
            post_data = [
                ('def_host_name', self.host_name),
                ('def_host_mac', '00:00:00:00:00:00'),
                ('host_ipad_input', new_ip),
                ('name_hidden', self.host_name),
                ('def_host_frompage', ''),
                ('def_host_from', ''),
                ('def_host_edt_but', ' 确定 ')
            ]

            # 添加所有IP地址到表单数据
            for ip in updated_ips:
                post_data.append(('def_host_ipad', ip))
            
            # 构造提交URL
            encoded_name = urllib.parse.quote(self.host_name, encoding='gb2312')
            submit_url = f"https://{self.host}/cgi/maincgi.cgi?Url=HostObj&Act=Edit&Name={encoded_name}"
            
            # 发送POST请求
            response = self.session.post(submit_url, data=post_data, verify=False)
            
            if response.status_code == 200:
                print(f"成功添加IP {new_ip} 到封禁列表")
                return True
            else:
                print(f"添加IP失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"添加IP过程中发生错误: {e}")
            return False
    
    def _validate_ip(self, ip: str) -> bool:
        """
        验证IP地址格式
        
        Args:
            ip: IP地址字符串
            
        Returns:
            bool: IP格式是否正确
        """
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        
        # 检查每个数字是否在0-255范围内
        parts = ip.split('.')
        for part in parts:
            if not (0 <= int(part) <= 255):
                return False
        
        return True


def main():
    """主函数，提供命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='防火墙IP管理工具')
    parser.add_argument('--host', required=True, help='防火墙主机地址')
    parser.add_argument('--username', required=True, help='用户名')
    parser.add_argument('--password', required=True, help='密码')
    parser.add_argument('--action', choices=['query', 'add'], required=True, help='操作类型')
    parser.add_argument('--ip', help='要添加的IP地址（仅在add操作时需要）')
    
    args = parser.parse_args()
    
    # 创建防火墙管理器实例
    fw_manager = FirewallManager(args.host, args.username, args.password)
    
    # 登录
    if not fw_manager.login():
        print("登录失败，程序退出")
        return
    
    # 执行操作
    if args.action == 'query':
        blocked_ips = fw_manager.get_blocked_ips()
        print(f"当前已封禁的IP地址: {blocked_ips}")
        
    elif args.action == 'add':
        if not args.ip:
            print("添加IP操作需要指定--ip参数")
            return
        
        success = fw_manager.add_blocked_ip(args.ip)
        if success:
            print(f"IP {args.ip} 添加成功")
        else:
            print(f"IP {args.ip} 添加失败")


if __name__ == "__main__":
    main()
