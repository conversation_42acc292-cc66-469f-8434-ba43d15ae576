#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙API测试脚本
演示如何使用防火墙API的两个主要接口
"""

from firewall_api import FirewallAPI
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def test_firewall_api():
    """测试防火墙API功能"""
    print("=" * 50)
    print("防火墙API测试")
    print("=" * 50)
    
    # 创建API实例（使用默认配置）
    api = FirewallAPI()
    
    print("\n【测试1】查询已封禁的IP地址")
    print("-" * 30)
    
    # 调用接口1：查询已封禁的IP
    result = api.get_blocked_ips()
    
    if result["success"]:
        print("✅ 查询成功")
        print(f"📊 当前已封禁IP数量: {result['data']['count']}")
        print("📋 已封禁IP列表:")
        for i, ip in enumerate(result['data']['blocked_ips'], 1):
            print(f"   {i}. {ip}")
    else:
        print("❌ 查询失败")
        print(f"错误信息: {result['message']}")
    
    print("\n" + "=" * 50)
    print("\n【测试2】封禁新的IP地址")
    print("-" * 30)
    
    # 测试要封禁的新IP
    test_ip = "*******"  # 根据您的需求，这是要添加的IP
    
    print(f"🎯 尝试封禁IP: {test_ip}")
    
    # 调用接口2：封禁新IP
    result = api.block_ip(test_ip)
    
    if result["success"]:
        print("✅ 操作成功")
        action = result['data']['action']
        if action == "blocked":
            print(f"🚫 IP {test_ip} 已成功添加到封禁列表")
        elif action == "already_blocked":
            print(f"ℹ️  IP {test_ip} 已在封禁列表中")
    else:
        print("❌ 操作失败")
        print(f"错误信息: {result['message']}")
    
    print("\n" + "=" * 50)
    print("\n【测试3】验证封禁结果")
    print("-" * 30)
    
    # 再次查询验证结果
    result = api.get_blocked_ips()
    
    if result["success"]:
        print("✅ 验证查询成功")
        print(f"📊 更新后已封禁IP数量: {result['data']['count']}")
        
        if test_ip in result['data']['blocked_ips']:
            print(f"✅ 确认IP {test_ip} 已在封禁列表中")
        else:
            print(f"⚠️  IP {test_ip} 未在封禁列表中")
            
        print("📋 最新封禁IP列表:")
        for i, ip in enumerate(result['data']['blocked_ips'], 1):
            marker = " 🆕" if ip == test_ip else ""
            print(f"   {i}. {ip}{marker}")
    else:
        print("❌ 验证查询失败")
        print(f"错误信息: {result['message']}")
    
    print("\n" + "=" * 50)


def test_invalid_ip():
    """测试无效IP地址的处理"""
    print("\n【额外测试】无效IP地址处理")
    print("-" * 30)
    
    api = FirewallAPI()
    
    # 测试无效IP
    invalid_ips = ["999.999.999.999", "192.168.1", "abc.def.ghi.jkl", "192.168.1.256"]
    
    for invalid_ip in invalid_ips:
        print(f"🧪 测试无效IP: {invalid_ip}")
        result = api.block_ip(invalid_ip)
        
        if not result["success"]:
            print(f"✅ 正确拒绝无效IP: {result['message']}")
        else:
            print(f"❌ 意外接受了无效IP")
        print()


if __name__ == "__main__":
    try:
        # 运行主要测试
        test_firewall_api()
        
        # 运行无效IP测试
        test_invalid_ip()
        
        print("\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
