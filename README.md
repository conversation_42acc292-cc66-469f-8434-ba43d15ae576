# 防火墙IP管理工具

基于Python3的防火墙IP封禁管理工具，模拟防火墙的登录、查询和添加封禁IP的操作流程。

## 功能特性

- 🔐 **自动登录**: 模拟防火墙登录流程，获取session认证
- 📋 **查询封禁IP**: 获取当前所有已封禁的IP地址列表
- 🚫 **添加封禁IP**: 将新的IP地址添加到封禁列表
- ✅ **IP格式验证**: 自动验证IP地址格式的有效性
- 🔄 **重复检查**: 避免重复添加已存在的IP地址

## 文件结构

```
├── firewall_manager.py    # 核心防火墙管理类
├── firewall_api.py        # 简化的API接口
├── test_firewall.py       # 测试和演示脚本
├── README.md              # 说明文档
├── login                  # 登录请求抓包数据
├── query                  # 查询请求抓包数据
└── add                    # 添加IP请求抓包数据
```

## 安装依赖

```bash
pip install requests urllib3
```

## 使用方法

### 1. 直接使用API接口（推荐）

```python
from firewall_api import FirewallAPI
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 创建API实例
api = FirewallAPI()

# 接口1: 查询已封禁的所有IP
result = api.get_blocked_ips()
if result["success"]:
    print(f"已封禁IP数量: {result['data']['count']}")
    print(f"IP列表: {result['data']['blocked_ips']}")

# 接口2: 封禁新的IP
result = api.block_ip("*******")
if result["success"]:
    print(f"IP封禁成功: {result['message']}")
```

### 2. 运行测试脚本

```bash
python test_firewall.py
```

### 3. 命令行使用

```bash
# 查询已封禁IP
python firewall_manager.py --host ************* --username wabsuper --password "whgD@955989" --action query

# 添加新的封禁IP
python firewall_manager.py --host ************* --username wabsuper --password "whgD@955989" --action add --ip *******
```

## API接口说明

### 接口1: 查询已封禁的所有IP

**方法**: `get_blocked_ips()`

**返回格式**:
```json
{
    "success": true,
    "message": "查询成功",
    "data": {
        "blocked_ips": ["*******", "*************", "***********", "***********", "*******"],
        "count": 5
    }
}
```

### 接口2: 封禁新的IP

**方法**: `block_ip(ip)`

**参数**:
- `ip` (string): 要封禁的IP地址

**返回格式**:
```json
{
    "success": true,
    "message": "IP ******* 封禁成功",
    "data": {
        "ip": "*******",
        "action": "blocked"
    }
}
```

## 配置说明

默认配置基于抓包数据：
- **主机地址**: `*************`
- **用户名**: `wabsuper`
- **密码**: `whgD@955989`
- **主机名**: `01-网安封禁内网地址`

如需修改配置，可以在创建`FirewallAPI`实例时传入参数：

```python
api = FirewallAPI(
    host="your_firewall_host",
    username="your_username", 
    password="your_password"
)
```

## 技术实现

### 1. 登录流程
- 发送POST请求到 `/cgi/maincgi.cgi?Url=Index`
- 提交用户名和密码
- 获取并保存session_id cookie

### 2. 查询流程
- 使用session_id访问主机对象编辑页面
- 解析HTML响应中的JavaScript变量 `ip_str`
- 提取并返回IP地址列表

### 3. 添加流程
- 获取当前IP列表
- 验证新IP格式和重复性
- 构造表单数据，包含所有IP地址
- 提交POST请求更新配置

## 注意事项

1. **SSL证书**: 脚本中禁用了SSL证书验证，适用于内网环境
2. **编码处理**: 正确处理中文主机名的URL编码（GB2312）
3. **错误处理**: 包含完整的异常处理和错误信息返回
4. **IP验证**: 自动验证IP地址格式，防止无效输入

## 示例输出

运行测试脚本的示例输出：

```
==================================================
防火墙API测试
==================================================

【测试1】查询已封禁的IP地址
------------------------------
✅ 查询成功
📊 当前已封禁IP数量: 5
📋 已封禁IP列表:
   1. *******
   2. *************
   3. ***********
   4. ***********
   5. *******

【测试2】封禁新的IP地址
------------------------------
🎯 尝试封禁IP: *******
✅ 操作成功
🚫 IP ******* 已成功添加到封禁列表

【测试3】验证封禁结果
------------------------------
✅ 验证查询成功
📊 更新后已封禁IP数量: 6
✅ 确认IP ******* 已在封禁列表中
📋 最新封禁IP列表:
   1. *******
   2. *************
   3. ***********
   4. ***********
   5. *******
   6. ******* 🆕
```

## 许可证

本项目仅供学习和测试使用。
